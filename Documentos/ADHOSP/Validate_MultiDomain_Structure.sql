-- Script SQL para validar a estrutura de múltiplos domínios
-- Execute este script para verificar se o ambiente está configurado corretamente

-- =====================================================
-- 1. VALIDAR DOMÍNIOS EXISTENTES
-- =====================================================
PRINT '=== DOMÍNIOS DO ACTIVE DIRECTORY ==='
SELECT 
    d.Ident_Domain AS 'Nome do Domínio',
    d.UID_ADSDomain AS 'UID do Domínio',
    d.DisplayName AS 'Nome de Exibição',
    CASE 
        WHEN d.UID_TSBAccountDef IS NOT NULL THEN 'SIM'
        ELSE 'NÃO'
    END AS 'Tem AccountDef <PERSON>',
    tad.Ident_TSBAccountDef AS 'AccountDef Padrão'
FROM ADSDomain d
LEFT JOIN TSBAccountDef tad ON d.UID_TSBAccountDef = tad.UID_TSBAccountDef
WHERE d.XMarkedForDeletion = 0
ORDER BY d.Ident_Domain;

-- =====================================================
-- 2. CONTAR CONTAS POR DOMÍNIO
-- =====================================================
PRINT ''
PRINT '=== CONTAS POR DOMÍNIO ==='
SELECT 
    d.Ident_Domain AS 'Domínio',
    COUNT(a.UID_ADSAccount) AS 'Total de Contas',
    COUNT(CASE WHEN a.UID_Person IS NOT NULL THEN 1 END) AS 'Contas com Pessoa',
    COUNT(CASE WHEN a.AccountDisabled = 0 THEN 1 END) AS 'Contas Ativas'
FROM ADSDomain d
LEFT JOIN ADSAccount a ON d.UID_ADSDomain = a.UID_ADSDomain AND a.XMarkedForDeletion = 0
WHERE d.XMarkedForDeletion = 0
GROUP BY d.Ident_Domain, d.UID_ADSDomain
ORDER BY d.Ident_Domain;

-- =====================================================
-- 3. ACCOUNT DEFINITIONS UTILIZADAS
-- =====================================================
PRINT ''
PRINT '=== ACCOUNT DEFINITIONS POR DOMÍNIO ==='
SELECT 
    d.Ident_Domain AS 'Domínio',
    tad.Ident_TSBAccountDef AS 'Account Definition',
    COUNT(a.UID_ADSAccount) AS 'Quantidade de Contas'
FROM ADSDomain d
LEFT JOIN ADSAccount a ON d.UID_ADSDomain = a.UID_ADSDomain AND a.XMarkedForDeletion = 0
LEFT JOIN TSBAccountDef tad ON a.UID_TSBAccountDef = tad.UID_TSBAccountDef
WHERE d.XMarkedForDeletion = 0
    AND tad.Ident_TSBAccountDef IS NOT NULL
GROUP BY d.Ident_Domain, tad.Ident_TSBAccountDef
ORDER BY d.Ident_Domain, tad.Ident_TSBAccountDef;

-- =====================================================
-- 4. IDENTIFICAR GERENTES COM CONTAS EM MÚLTIPLOS DOMÍNIOS
-- =====================================================
PRINT ''
PRINT '=== GERENTES COM CONTAS EM MÚLTIPLOS DOMÍNIOS ==='
WITH ManagersMultipleDomains AS (
    SELECT 
        p.UID_Person,
        p.CentralAccount,
        COUNT(DISTINCT a.UID_ADSDomain) AS DomainCount
    FROM Person p
    INNER JOIN ADSAccount a ON p.UID_Person = a.UID_Person
    INNER JOIN TSBAccountDef tad ON a.UID_TSBAccountDef = tad.UID_TSBAccountDef
    WHERE p.XMarkedForDeletion = 0
        AND a.XMarkedForDeletion = 0
        AND tad.Ident_TSBAccountDef IN ('AD-User') -- Adicione outras Account Definitions conforme necessário
        AND EXISTS (
            SELECT 1 FROM Person subordinate 
            WHERE subordinate.UID_PersonHead = p.UID_Person 
                AND subordinate.XMarkedForDeletion = 0
        )
    GROUP BY p.UID_Person, p.CentralAccount
    HAVING COUNT(DISTINCT a.UID_ADSDomain) > 1
)
SELECT 
    mmd.CentralAccount AS 'Login do Gerente',
    mmd.DomainCount AS 'Qtd Domínios',
    STRING_AGG(d.Ident_Domain, ', ') AS 'Domínios'
FROM ManagersMultipleDomains mmd
INNER JOIN ADSAccount a ON mmd.UID_Person = a.UID_Person
INNER JOIN ADSDomain d ON a.UID_ADSDomain = d.UID_ADSDomain
WHERE a.XMarkedForDeletion = 0
GROUP BY mmd.CentralAccount, mmd.DomainCount
ORDER BY mmd.CentralAccount;

-- =====================================================
-- 5. CASOS PROBLEMÁTICOS: SUBORDINADOS SEM GERENTE NO MESMO DOMÍNIO
-- =====================================================
PRINT ''
PRINT '=== SUBORDINADOS SEM GERENTE NO MESMO DOMÍNIO ==='
WITH SubordinateManagerDomains AS (
    SELECT 
        sub.CentralAccount AS SubordinateLogin,
        mgr.CentralAccount AS ManagerLogin,
        sub_acc.UID_ADSDomain AS SubordinateDomain,
        mgr_acc.UID_ADSDomain AS ManagerDomain,
        sub_dom.Ident_Domain AS SubordinateDomainName,
        mgr_dom.Ident_Domain AS ManagerDomainName
    FROM Person sub
    INNER JOIN Person mgr ON sub.UID_PersonHead = mgr.UID_Person
    INNER JOIN ADSAccount sub_acc ON sub.UID_Person = sub_acc.UID_Person
    INNER JOIN ADSAccount mgr_acc ON mgr.UID_Person = mgr_acc.UID_Person
    INNER JOIN ADSDomain sub_dom ON sub_acc.UID_ADSDomain = sub_dom.UID_ADSDomain
    INNER JOIN ADSDomain mgr_dom ON mgr_acc.UID_ADSDomain = mgr_dom.UID_ADSDomain
    INNER JOIN TSBAccountDef sub_tad ON sub_acc.UID_TSBAccountDef = sub_tad.UID_TSBAccountDef
    INNER JOIN TSBAccountDef mgr_tad ON mgr_acc.UID_TSBAccountDef = mgr_tad.UID_TSBAccountDef
    WHERE sub.XMarkedForDeletion = 0
        AND mgr.XMarkedForDeletion = 0
        AND sub_acc.XMarkedForDeletion = 0
        AND mgr_acc.XMarkedForDeletion = 0
        AND sub_tad.Ident_TSBAccountDef IN ('AD-User') -- Account Definitions padrão
        AND mgr_tad.Ident_TSBAccountDef IN ('AD-User') -- Account Definitions padrão
        AND sub_acc.UID_ADSDomain != mgr_acc.UID_ADSDomain -- Domínios diferentes
)
SELECT TOP 20
    SubordinateLogin AS 'Login Subordinado',
    ManagerLogin AS 'Login Gerente',
    SubordinateDomainName AS 'Domínio Subordinado',
    ManagerDomainName AS 'Domínio Gerente',
    'CAMPO FICARÁ VAZIO' AS 'Resultado Esperado'
FROM SubordinateManagerDomains
ORDER BY SubordinateLogin;

-- =====================================================
-- 6. CONFIGURAÇÕES NECESSÁRIAS
-- =====================================================
PRINT ''
PRINT '=== CONFIGURAÇÕES DO SISTEMA ==='
SELECT 
    'Custom\AD\AccountDefforTerceiro' AS 'Parâmetro',
    ISNULL(
        (SELECT TOP 1 Value FROM QBMConfigParm WHERE Path = 'Custom\AD\AccountDefforTerceiro'),
        'NÃO CONFIGURADO'
    ) AS 'Valor';

-- =====================================================
-- 7. RESUMO ESTATÍSTICO
-- =====================================================
PRINT ''
PRINT '=== RESUMO ESTATÍSTICO ==='
SELECT 
    'Total de Domínios' AS 'Métrica',
    CAST(COUNT(*) AS VARCHAR(10)) AS 'Valor'
FROM ADSDomain 
WHERE XMarkedForDeletion = 0

UNION ALL

SELECT 
    'Total de Contas AD' AS 'Métrica',
    CAST(COUNT(*) AS VARCHAR(10)) AS 'Valor'
FROM ADSAccount 
WHERE XMarkedForDeletion = 0

UNION ALL

SELECT 
    'Contas com Pessoa Associada' AS 'Métrica',
    CAST(COUNT(*) AS VARCHAR(10)) AS 'Valor'
FROM ADSAccount 
WHERE XMarkedForDeletion = 0 AND UID_Person IS NOT NULL

UNION ALL

SELECT 
    'Pessoas com Gerente Definido' AS 'Métrica',
    CAST(COUNT(*) AS VARCHAR(10)) AS 'Valor'
FROM Person 
WHERE XMarkedForDeletion = 0 AND UID_PersonHead IS NOT NULL

UNION ALL

SELECT 
    'Account Definitions Ativas' AS 'Métrica',
    CAST(COUNT(*) AS VARCHAR(10)) AS 'Valor'
FROM TSBAccountDef 
WHERE XMarkedForDeletion = 0;

-- =====================================================
-- 8. RECOMENDAÇÕES
-- =====================================================
PRINT ''
PRINT '=== RECOMENDAÇÕES ==='
PRINT '1. Verifique se todos os domínios têm Account Definitions configuradas'
PRINT '2. Configure o parâmetro Custom\AD\AccountDefforTerceiro se necessário'
PRINT '3. Monitore casos onde subordinados e gerentes estão em domínios diferentes'
PRINT '4. Considere criar Account Definitions específicas por domínio se necessário'
PRINT '5. Teste o script em ambiente de desenvolvimento antes da produção'
