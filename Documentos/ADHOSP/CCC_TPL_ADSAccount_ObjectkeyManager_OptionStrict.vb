Option Strict On

Function CCC_tpl_ADSAccount_ObjectkeyManager(strUIDLineManager As String) As String

    Dim strResult As String = ""
    Try
        '-> Only if there is a Line Manager on person configured
        If Not String.IsNullOrEmpty(strUIDLineManager) Then
            '-> Get the domain of the current account to find manager account in the same domain
            Dim strCurrentAccountDomain As String = GetCurrentAccountDomain()
            
            '-> Account Definitions for standard users
            Const StdUsr_Account_Def As String = "AD-User"
            Dim StdUsr_Account_Def_terceiro As String = GetTerceiroAccountDef()
            
            Dim ColADAccounts As IEntityCollection
            Dim qADSAccount As Query
            
            '-> Build the query based on whether we have domain information
            If Not String.IsNullOrEmpty(strCurrentAccountDomain) Then
                '-> PREFERRED: Search for manager account in the same domain with matching Account Definition
                qADSAccount = BuildDomainSpecificQuery(strUIDLineManager, str<PERSON><PERSON><PERSON><PERSON><PERSON>untD<PERSON>in, StdUsr_Account_Def, StdUsr_Account_Def_terceiro)
            Else
                '-> FALLBACK: If current domain is unknown, get all manager accounts
                qADSAccount = BuildFallbackQuery(strUIDLineManager, StdUsr_Account_Def, StdUsr_Account_Def_terceiro)
            End If

            ColADAccounts = Session.Source.GetCollection(qADSAccount)
            
            '-> Process the results to find the best manager account
            If ColADAccounts.Count > 0 Then
                strResult = ColADAccounts(0).GetValue("xobjectkey").String
            End If
            '-> If no manager account found, strResult remains empty (as requested)
        End If
    Catch ex As Exception
        Throw New Exception("Error in script CCC_tpl_ADSAccount_ObjectkeyManager: " & ex.Message, ex)
    End Try
    
    Return strResult

End Function

Private Function GetCurrentAccountDomain() As String
    Dim strCurrentAccountDomain As String = ""
    
    ' Method 1: From Variables (template context)
    Try
        Dim domainValue As Object = Variables("UID_ADSDomain")
        If domainValue IsNot Nothing AndAlso Not String.IsNullOrEmpty(domainValue.ToString()) Then
            Return domainValue.ToString()
        End If
    Catch
        ' Continue to next method
    End Try
    
    ' Method 2: From Entity (if available)
    Try
        Dim entityValue As Object = Entity.GetValue("UID_ADSDomain")
        If entityValue IsNot Nothing AndAlso Not String.IsNullOrEmpty(entityValue.ToString()) Then
            Return entityValue.ToString()
        End If
    Catch
        ' Continue to next method
    End Try
    
    ' Method 3: From FK reference if available
    Try
        Dim fkValue As Object = Variables("FK(UID_ADSDomain).UID_ADSDomain")
        If fkValue IsNot Nothing AndAlso Not String.IsNullOrEmpty(fkValue.ToString()) Then
            Return fkValue.ToString()
        End If
    Catch
        ' Continue to next method
    End Try
    
    ' Method 4: Try to get from current person's existing AD accounts
    Try
        Dim personValue As Object = Variables("UID_Person")
        If personValue IsNot Nothing AndAlso Not String.IsNullOrEmpty(personValue.ToString()) Then
            Dim strCurrentPerson As String = personValue.ToString()
            Dim qCurrentDomain As Query = Query.From("ADSAccount") _
                                .Where(String.Format("uid_person = '{0}'", strCurrentPerson)) _
                                .Select("UID_ADSDomain")
            Dim colCurrentDomain As IEntityCollection = Session.Source.GetCollection(qCurrentDomain)
            If colCurrentDomain.Count > 0 Then
                Return colCurrentDomain(0).GetValue("UID_ADSDomain").String
            End If
        End If
    Catch
        ' All methods failed, return empty string
    End Try
    
    Return strCurrentAccountDomain
End Function

Private Function GetTerceiroAccountDef() As String
    Try
        Dim configValue As Object = Connection.GetConfigParm("Custom\AD\AccountDefforTerceiro")
        If configValue IsNot Nothing Then
            Return configValue.ToString()
        End If
    Catch
        ' Return empty string if config not found
    End Try
    Return ""
End Function

Private Function BuildDomainSpecificQuery(managerUID As String, domainUID As String, stdAccountDef As String, terceiroAccountDef As String) As Query
    '-> Search for manager account in the same domain with matching Account Definition
    Dim domainSpecificQuery As String = String.Format("uid_person = '{0}' and UID_ADSDomain = '{1}'", managerUID, domainUID)
    
    '-> Build Account Definition filter
    Dim accountDefList As New List(Of String)
    accountDefList.Add(stdAccountDef)
    If Not String.IsNullOrEmpty(terceiroAccountDef) Then
        accountDefList.Add(terceiroAccountDef)
    End If
    
    Dim accountDefFilter As String = String.Format("UID_TSBAccountDef in (select uid_TSBAccountDef from TSBAccountDef where Ident_TSBAccountDef in ('{0}'))", String.Join("','", accountDefList))
    
    '-> Also include the domain's default Account Definition if it exists
    Dim domainDefaultAccountDef As String = String.Format("UID_TSBAccountDef in (select UID_TSBAccountDef from ADSDomain where UID_ADSDomain = '{0}' and UID_TSBAccountDef is not null)", domainUID)
    
    '-> Combine the filters with OR logic
    Dim combinedAccountDefFilter As String = String.Format("({0} OR {1})", accountDefFilter, domainDefaultAccountDef)
    
    Return Query.From("ADSAccount") _
           .Where(String.Format("{0} and ({1})", domainSpecificQuery, combinedAccountDefFilter)) _
           .Select("xobjectkey")
End Function

Private Function BuildFallbackQuery(managerUID As String, stdAccountDef As String, terceiroAccountDef As String) As Query
    '-> Build Account Definition filter for fallback
    Dim accountDefList As New List(Of String)
    accountDefList.Add(stdAccountDef)
    If Not String.IsNullOrEmpty(terceiroAccountDef) Then
        accountDefList.Add(terceiroAccountDef)
    End If
    
    Return Query.From("ADSAccount") _
           .Where(String.Format("uid_person = '{0}' and UID_TSBAccountDef in (select uid_TSBAccountDef from TSBAccountDef where Ident_TSBAccountDef in ('{1}'))", _
                  managerUID, String.Join("','", accountDefList))) _
           .Select("xobjectkey", "UID_ADSDomain")
End Function
