' Script de teste para validar o funcionamento do CCC_TPL_ADSAccount_ObjectkeyManager
' Este script pode ser usado para testar diferentes cenários

Sub TestManagerAccountScript()
    
    Dim testResults As New System.Text.StringBuilder()
    testResults.AppendLine("=== TESTE DO SCRIPT CCC_TPL_ADSAccount_ObjectkeyManager ===")
    testResults.AppendLine("")
    
    Try
        ' Teste 1: Gerente com conta no mesmo domínio
        testResults.AppendLine("TESTE 1: Gerente com conta no mesmo domínio")
        Dim result1 As String = TestScenario("UID_GERENTE_MESMO_DOMINIO", "UID_DOMINIO_A")
        testResults.AppendLine("Resultado: " & If(String.IsNullOrEmpty(result1), "VAZIO", result1))
        testResults.AppendLine("")
        
        ' Teste 2: Gerente com conta em domínio diferente
        testResults.AppendLine("TESTE 2: Gerente com conta em domínio diferente")
        Dim result2 As String = TestScenario("UID_GERENTE_OUTRO_DOMINIO", "UID_DOMINIO_A")
        testResults.AppendLine("Resultado: " & If(String.IsNullOrEmpty(result2), "VAZIO (ESPERADO)", result2))
        testResults.AppendLine("")
        
        ' Teste 3: Gerente sem conta AD
        testResults.AppendLine("TESTE 3: Gerente sem conta AD")
        Dim result3 As String = TestScenario("UID_GERENTE_SEM_CONTA", "UID_DOMINIO_A")
        testResults.AppendLine("Resultado: " & If(String.IsNullOrEmpty(result3), "VAZIO (ESPERADO)", result3))
        testResults.AppendLine("")
        
        ' Teste 4: Domínio atual não identificado (fallback)
        testResults.AppendLine("TESTE 4: Domínio atual não identificado (fallback)")
        Dim result4 As String = TestScenario("UID_GERENTE_QUALQUER", "")
        testResults.AppendLine("Resultado: " & If(String.IsNullOrEmpty(result4), "VAZIO", result4))
        testResults.AppendLine("")
        
        ' Teste 5: Validar Account Definitions por domínio
        testResults.AppendLine("TESTE 5: Validar Account Definitions por domínio")
        ValidateAccountDefinitionsByDomain(testResults)
        testResults.AppendLine("")
        
        ' Teste 6: Validar múltiplos domínios
        testResults.AppendLine("TESTE 6: Validar múltiplos domínios")
        ValidateMultipleDomains(testResults)
        
    Catch ex As Exception
        testResults.AppendLine("ERRO DURANTE OS TESTES: " & ex.Message)
        testResults.AppendLine("Stack Trace: " & ex.StackTrace)
    End Try
    
    ' Exibir resultados
    System.Windows.Forms.MessageBox.Show(testResults.ToString(), "Resultados dos Testes")
    
End Sub

Function TestScenario(managerUID As String, currentDomain As String) As String
    Try
        ' Simular contexto de domínio atual se fornecido
        If Not String.IsNullOrEmpty(currentDomain) Then
            Variables.Put("UID_ADSDomain", currentDomain)
        Else
            Variables.Remove("UID_ADSDomain")
        End If
        
        ' Chamar a função principal
        Return CCC_tpl_ADSAccount_ObjectkeyManager(managerUID)
        
    Catch ex As Exception
        Return "ERRO: " & ex.Message
    End Try
End Function

Sub ValidateAccountDefinitionsByDomain(ByRef results As System.Text.StringBuilder)
    Try
        ' Consultar domínios e suas Account Definitions
        Dim qDomains = Query.From("ADSDomain") _
                      .Where("UID_TSBAccountDef is not null") _
                      .Select("Ident_Domain", "UID_TSBAccountDef", "UID_ADSDomain")
        
        Dim colDomains = Session.Source.GetCollection(qDomains)
        
        results.AppendLine("Domínios com Account Definitions específicas:")
        For Each domain As IEntity In colDomains
            Dim domainName As String = domain.GetValue("Ident_Domain").String
            Dim accountDefUID As String = domain.GetValue("UID_TSBAccountDef").String
            
            ' Buscar nome da Account Definition
            Dim qAccountDef = Query.From("TSBAccountDef") _
                             .Where(String.Format("UID_TSBAccountDef = '{0}'", accountDefUID)) _
                             .Select("Ident_TSBAccountDef")
            
            Dim colAccountDef = Session.Source.GetCollection(qAccountDef)
            Dim accountDefName As String = If(colAccountDef.Count > 0, colAccountDef(0).GetValue("Ident_TSBAccountDef").String, "DESCONHECIDA")
            
            results.AppendLine("  - " & domainName & " -> " & accountDefName)
        Next
        
        If colDomains.Count = 0 Then
            results.AppendLine("  Nenhum domínio com Account Definition específica encontrado")
        End If
        
    Catch ex As Exception
        results.AppendLine("ERRO ao validar Account Definitions: " & ex.Message)
    End Try
End Sub

Sub ValidateMultipleDomains(ByRef results As System.Text.StringBuilder)
    Try
        ' Consultar todos os domínios
        Dim qDomains = Query.From("ADSDomain") _
                      .Select("Ident_Domain", "UID_ADSDomain")
        
        Dim colDomains = Session.Source.GetCollection(qDomains)
        
        results.AppendLine("Domínios disponíveis no sistema:")
        For Each domain As IEntity In colDomains
            Dim domainName As String = domain.GetValue("Ident_Domain").String
            Dim domainUID As String = domain.GetValue("UID_ADSDomain").String
            
            ' Contar contas neste domínio
            Dim qAccounts = Query.From("ADSAccount") _
                           .Where(String.Format("UID_ADSDomain = '{0}'", domainUID)) _
                           .Select("UID_ADSAccount")
            
            Dim colAccounts = Session.Source.GetCollection(qAccounts)
            
            results.AppendLine("  - " & domainName & " (" & colAccounts.Count & " contas)")
        Next
        
        results.AppendLine("Total de domínios: " & colDomains.Count)
        
    Catch ex As Exception
        results.AppendLine("ERRO ao validar múltiplos domínios: " & ex.Message)
    End Try
End Sub

' Função auxiliar para testar casos específicos
Function TestSpecificCase(personUID As String, managerUID As String, expectedDomain As String) As String
    Try
        ' Configurar contexto
        Variables.Put("UID_Person", personUID)
        Variables.Put("UID_ADSDomain", expectedDomain)
        
        ' Executar teste
        Dim result As String = CCC_tpl_ADSAccount_ObjectkeyManager(managerUID)
        
        ' Validar se o resultado está no domínio esperado
        If Not String.IsNullOrEmpty(result) Then
            Dim qValidation = Query.From("ADSAccount") _
                             .Where(String.Format("XObjectKey = '{0}'", result)) _
                             .Select("UID_ADSDomain")
            
            Dim colValidation = Session.Source.GetCollection(qValidation)
            If colValidation.Count > 0 Then
                Dim resultDomain As String = colValidation(0).GetValue("UID_ADSDomain").String
                If resultDomain = expectedDomain Then
                    Return "SUCESSO: Conta do gerente encontrada no domínio correto"
                Else
                    Return "AVISO: Conta do gerente encontrada em domínio diferente (" & resultDomain & ")"
                End If
            End If
        End If
        
        Return "INFO: Nenhuma conta de gerente encontrada (campo ficará vazio)"
        
    Catch ex As Exception
        Return "ERRO: " & ex.Message
    End Try
End Function
