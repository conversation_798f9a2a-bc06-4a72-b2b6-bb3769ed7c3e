Function CCC_tpl_ADSAccount_ObjectkeyManager(strUIDLineManager As String) As String

      Dim strResult As String = ""
      Try
        '-> Only if I there is a Line Manager on person configured
        If strUIDLineManager.Length() > 0 Then
          '-> A Person can have more than one AD Account assigned. Assuming there is only one domain it could be e.g. a std. user 
          '     and an admin account. With the name of the assigned Std. user account definition it is ensured to get only the 
          '     std user account back.
          '-> *** MUST BE UPDATED BASED ON YOUR ACCOUNTDEF NAME ***
        Const StdUsr_Account_Def As String = "AD-User"
		Dim StdUsr_Account_Def_terceiro As String = Connection.GetConfigParm("Custom\AD\AccountDefforTerceiro").ToString

          Dim ColADAccounts As IEntityCollection
          Dim qADSAccount = Query.From("ADSAccount") _
                            .Where(String.Format("uid_person = '{0}' and UID_TSBAccountDef in (select uid_TSBAccountDef from TSBAccountDef where Ident_TSBAccountDef in ('{1}','{2}'))", strUIDLineManager, StdUsr_Account_Def, StdUsr_Account_Def_terceiro)) _
                            .Select("xobjectkey")

          ColADAccounts = Session.Source.GetCollection(qADSAccount)
          For Each elm As IEntity In ColADAccounts
            '-> It is a 
            strResult = elm.GetValue("xobjectkey").String
          Next
        End If
      Catch ex As Exception
        Throw New Exception("Error in script CCC_tpl_ADSAccount_ObjectkeyManager: ", ex)

      Finally
        CCC_tpl_ADSAccount_ObjectkeyManager = strResult

      End Try

End Function