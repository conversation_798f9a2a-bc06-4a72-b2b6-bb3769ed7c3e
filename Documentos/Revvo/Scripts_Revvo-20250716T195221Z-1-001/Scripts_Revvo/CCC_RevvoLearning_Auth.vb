Imports Newtonsoft.Json.Linq
Imports Newtonsoft.Json
Imports System.Net

Public Function CCC_RevvoLearning_Auth(ByVal clientId As String, ByVal clientSecret As String) As String
    Dim accessToken As String = Nothing ' Variável para armazenar o token de acesso

    Try
        Using client As New WebClient()
            ' Define o Content-Type para indicar que o corpo da requisição é JSON
            client.Headers(HttpRequestHeader.ContentType) = "application/json"

            ' Prepara os dados JSON para a requisição de autenticação
            ' É crucial que a string JSON seja formatada corretamente com aspas duplas escapadas.
            Dim jsonData As String = String.Format(
                "{{""grant_type"": ""client_credentials"", ""client_id"": ""{0}"", ""client_secret"": ""{1}""}}",
                clientId, clientSecret
            )

            ' Converte a string JSON para um array de bytes usando UTF8, que é padrão para JSON
            Dim requestBytes As Byte() = System.Text.Encoding.UTF8.GetBytes(jsonData)

            ' Realiza a requisição POST para o endpoint de token
            Dim tokenURL As String = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\TokenURL")

            Dim responseBytes As Byte() = client.UploadData(tokenURL, "POST", requestBytes)

            ' Converte a resposta em bytes para uma string usando UTF8
            Dim responseString As String = System.Text.Encoding.UTF8.GetString(responseBytes)

            ' Desserializa a string JSON da resposta para um objeto JObject
            Dim jsonResponse As JObject = JObject.Parse(responseString)

            ' Verifica se o objeto JSON e o campo 'access_token' existem antes de tentar acessá-los
            If jsonResponse IsNot Nothing AndAlso jsonResponse("access_token") IsNot Nothing Then
                accessToken = jsonResponse("access_token").ToString()

                Console.WriteLine("Token de acesso obtido com sucesso.")
            Else
                Console.WriteLine("Resposta JSON não contém 'access_token' ou está vazia.")
                Console.WriteLine("Resposta completa: " & responseString)
            End If

        End Using ' Garante que o WebClient seja descartado corretamente

    Catch ex As WebException
        ' Captura exceções específicas de requisições web (e.g., erros HTTP 4xx, 5xx)
        Console.WriteLine("Erro na requisição web durante a autenticação: " & ex.Message)
        If ex.Response IsNot Nothing Then
            Using reader As New System.IO.StreamReader(ex.Response.GetResponseStream())
                Dim errorResponse As String = reader.ReadToEnd()
                Console.WriteLine("Resposta de erro do servidor: " & errorResponse)
            End Using
        End If
        ' Em caso de erro, retorna Nothing para indicar falha na obtenção do token
        Return Nothing
    Catch ex As Exception
        ' Captura outras exceções genéricas
        Console.WriteLine("Ocorreu um erro inesperado durante a autenticação: " & ex.Message)
        ' Em caso de erro, retorna Nothing
        Return Nothing
    End Try

    Return accessToken ' Retorna o token de acesso (ou Nothing se houve erro)
End Function
